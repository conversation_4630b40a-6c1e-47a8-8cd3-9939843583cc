{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Python Debugger: transaction-classifier",
            "type": "debugpy",
            "request": "launch",
            "module": "transaction_classifier.cli",
            "args": ["data/banking_transactions_Superstep-Zaelab-TMG - NS-.csv", "data/output.csv"],
            "console": "integratedTerminal",
            "python": ".venv/bin/python"
        }
    ]
}