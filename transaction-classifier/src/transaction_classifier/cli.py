"""Command-line interface for transaction classifier."""

import click
import time
from pathlib import Path
from dotenv import load_dotenv
from .classifier import TransactionClassifier

# Load environment variables from .env file
load_dotenv()


@click.command()
@click.argument("input_file", type=click.Path(exists=True, path_type=Path))
@click.argument("output_file", type=click.Path(path_type=Path))
@click.option(
    "--batch-size",
    default=50,
    help="Number of transactions to process in each batch",
)
@click.option(
    "--model",
    default="openai:gpt-4.1",
    help="Model to use for classification",
)
@click.option(
    "--max-retries",
    default=1,
    help="Maximum number of retry attempts for failed batches",
)
@click.option(
    "--max-parallel",
    default=3,
    help="Maximum number of batches to process in parallel",
)
@click.option(
    "--openai-url",
    default=None,
    help="Custom OpenAI-compatible API base URL (e.g., http://localhost:11434/v1 for Ollama)",
)
def main(input_file: Path, output_file: Path, batch_size: int, model: str, max_retries: int, max_parallel: int, openai_url: str):
    """Classify banking transactions using LLM.
    
    INPUT_FILE: Path to CSV or Parquet file containing transaction data
    OUTPUT_FILE: Path to output CSV file with classifications
    """
        
    click.echo(f"Processing {input_file} with batch size {batch_size}, max retries {max_retries}, and max parallel {max_parallel}")
    
    # Start timing
    start_time = time.time()
    
    classifier = TransactionClassifier(model=model, batch_size=batch_size, max_retries=max_retries, max_parallel=max_parallel, openai_url=openai_url)
    classifier.process_file(input_file, output_file)
    
    # End timing
    end_time = time.time()
    processing_time = end_time - start_time
    
    click.echo(f"Classification complete. Results saved to {output_file}")
    click.echo(f"Total processing time: {processing_time:.2f} seconds")


if __name__ == "__main__":
    main()