"""Transaction classification using LLM and DuckDB."""

import duckdb
import pandas as pd
import os
import json
import time
import asyncio
from pathlib import Path
from typing import List, Dict
from pydantic import BaseModel
from pydantic_ai import Agent
from pydantic_ai.models.openai import OpenAIModel
from pydantic_ai.providers.openai import OpenAIProvider


class TransactionCategory(BaseModel):
    """Model for transaction category classification."""
    category: str
    confidence: float
    reasoning: str


class BatchClassificationResult(BaseModel):
    """Model for batch classification results."""
    classifications: List[TransactionCategory]


class TransactionClassifier:
    """Classifies banking transactions using LLM."""
    
    def __init__(self, model: str, batch_size: int = 50, max_retries: int = 1, max_parallel: int = 3, openai_url: str = None):
        self.model_name = model
        self.batch_size = batch_size
        self.max_retries = max_retries
        self.max_parallel = max_parallel
        self.openai_url = openai_url
        
        # Create the model instance
        model_instance = self._create_model_instance(model, openai_url)
        
        # Initialize agents for inflow and outflow
        self.inflow_agent = Agent(
            model=model_instance,
            output_type=BatchClassificationResult,
            system_prompt=self._get_inflow_system_prompt(),
        )
        
        self.outflow_agent = Agent(
            model=model_instance,
            output_type=BatchClassificationResult,
            system_prompt=self._get_outflow_system_prompt(),
        )
    
    def _create_model_instance(self, model: str, openai_url: str = None):
        """Create a model instance, optionally with a custom OpenAI provider."""
        if openai_url:
            # Use custom OpenAI provider with the specified base URL
            provider = OpenAIProvider(base_url=openai_url)
            return OpenAIModel(model, provider=provider)
        else:
            # Use default model (could be OpenAI or other providers)
            return model
    
    def _load_data(self, file_path: Path) -> pd.DataFrame:
        """Load transaction data from CSV or Parquet file using DuckDB."""
        conn = duckdb.connect()
        
        if file_path.suffix.lower() == '.csv':
            df = conn.execute(f"SELECT * FROM read_csv_auto('{file_path}')").df()
        elif file_path.suffix.lower() == '.parquet':
            df = conn.execute(f"SELECT * FROM read_parquet('{file_path}')").df()
        else:
            raise ValueError(f"Unsupported file format: {file_path.suffix}")
        
        conn.close()
        return df
    
    def _partition_transactions(self, df: pd.DataFrame) -> Dict[str, pd.DataFrame]:
        """Partition transactions into inflow and outflow based on amount."""
        inflow = df[df['amount'] > 0].copy()
        outflow = df[df['amount'] < 0].copy()
        
        print(f"Partitioned {len(df)} transactions:")
        print(f"  Inflow transactions: {len(inflow)}")
        print(f"  Outflow transactions: {len(outflow)}")
        
        return {
            'inflow': inflow,
            'outflow': outflow
        }
    
    def _format_transactions_for_llm(self, transactions: pd.DataFrame) -> str:
        """Format transactions as JSON for LLM input."""
        transaction_list = []
        for idx, row in transactions.iterrows():
            # Common transaction fields - adapt based on your data structure
            # Handle pandas Timestamp objects by converting to string
            date_value = row.get('postedDate', '')
            if pd.notna(date_value):
                if hasattr(date_value, 'strftime'):
                    date_value = date_value.strftime('%Y-%m-%d')
                else:
                    date_value = str(date_value)
            
            transaction = {
                "transaction_id": idx,
                "account": str(row.get('account', '')),
                "date": date_value,
                "description": str(row.get('description', '')),
                "amount": float(row.get('amount', 0)),
                "ocrolus_tag": str(row.get('ocrolus_tag', ''))
            }
            transaction_list.append(transaction)
        
        return json.dumps(transaction_list, indent=2)
    
    async def _classify_batch(self, transactions: pd.DataFrame, transaction_type: str) -> List[TransactionCategory]:
        """Classify a batch of transactions using the appropriate LLM agent."""
        formatted_transactions = self._format_transactions_for_llm(transactions)
        
        prompt = f"""Classify the following {len(transactions)} {transaction_type} transactions:

{formatted_transactions}

Return a classification for each transaction in the same order as provided in the JSON array."""
        
        # Use appropriate agent based on transaction type
        if transaction_type == 'inflow':
            result = await self.inflow_agent.run(prompt)
        else:  # outflow
            result = await self.outflow_agent.run(prompt)
            
        classifications = result.output.classifications
        return classifications
    
    async def _process_single_batch(self, batch: pd.DataFrame, batch_num: int, transaction_type: str, main_df: pd.DataFrame, semaphore: asyncio.Semaphore) -> bool:
        """Process a single batch with semaphore control and retry logic."""
        async with semaphore:
            batch_start_idx = batch.index[0]  # Get the first index of the batch
            batch_end_idx = batch.index[-1]   # Get the last index of the batch
            
            print(f"Processing {transaction_type} batch {batch_num}: indices {batch_start_idx}-{batch_end_idx}")
            
            # Always set the inflow/outflow field for all rows in the batch
            for j in range(len(batch)):
                batch_row = batch.iloc[j]
                main_row_idx = batch_row.name  # Get the original index
                
                if main_row_idx < len(main_df):
                    main_df.iloc[main_row_idx, main_df.columns.get_loc('inflow/outflow')] = 'Inflow' if transaction_type == 'inflow' else 'Outflow'
            
            # Retry logic
            success = False
            last_error = None
            
            for attempt in range(self.max_retries + 1):  # +1 because we want initial attempt + retries
                if attempt > 0:
                    print(f"Retrying {transaction_type} batch {batch_num} (attempt {attempt + 1}/{self.max_retries + 1})")
                
                # Start timing for this attempt
                batch_start_time = time.time()
                
                try:
                    classifications = await self._classify_batch(batch, transaction_type)
                    
                    # Update dataframe with results
                    for j, classification in enumerate(classifications):
                        # Find the corresponding row in the main dataframe
                        batch_row = batch.iloc[j]
                        main_row_idx = batch_row.name  # Get the original index
                        
                        if main_row_idx < len(main_df):
                            main_df.iloc[main_row_idx, main_df.columns.get_loc('category')] = classification.category
                            main_df.iloc[main_row_idx, main_df.columns.get_loc('confidence')] = classification.confidence
                            main_df.iloc[main_row_idx, main_df.columns.get_loc('reasoning')] = classification.reasoning
                    
                    success = True
                    last_error = None  # Clear any previous error on success
                    batch_end_time = time.time()
                    batch_time = batch_end_time - batch_start_time
                    
                    if attempt > 0:
                        print(f"{transaction_type.capitalize()} batch {batch_num} succeeded on retry attempt {attempt + 1} in {batch_time:.2f} seconds")
                    else:
                        print(f"{transaction_type.capitalize()} batch {batch_num} completed in {batch_time:.2f} seconds")
                    break
                    
                except Exception as e:
                    last_error = e
                    batch_end_time = time.time()
                    batch_time = batch_end_time - batch_start_time
                    
                    if attempt < self.max_retries:
                        print(f"Error processing {transaction_type} batch {batch_num} on attempt {attempt + 1}: {e} (will retry)")
                    else:
                        print(f"Error processing {transaction_type} batch {batch_num} on final attempt {attempt + 1}: {e}")
                    
                    print(f"{transaction_type.capitalize()} batch {batch_num} attempt {attempt + 1} failed after {batch_time:.2f} seconds")
            
            # If all attempts failed, update dataframe with error information
            if not success:
                print(f"{transaction_type.capitalize()} batch {batch_num} failed after {self.max_retries + 1} attempts. Final error: {last_error}")
                
                # Update dataframe with error information for all rows in the batch
                for j in range(len(batch)):
                    batch_row = batch.iloc[j]
                    main_row_idx = batch_row.name  # Get the original index
                    
                    if main_row_idx < len(main_df):
                        # Clear any previous successful data and set error information
                        main_df.iloc[main_row_idx, main_df.columns.get_loc('category')] = ''
                        main_df.iloc[main_row_idx, main_df.columns.get_loc('confidence')] = 0.0
                        main_df.iloc[main_row_idx, main_df.columns.get_loc('reasoning')] = f"ERROR: {last_error}"
            
            return success
    
    def process_file(self, input_file: Path, output_file: Path) -> None:
        """Process the entire file and save results."""
        # Load data
        df = self._load_data(input_file)
        print(f"Loaded {len(df)} transactions from {input_file}")
        
        # Partition transactions into inflow and outflow
        partitioned_data = self._partition_transactions(df)
        
        # Add columns for classification results
        df['inflow/outflow'] = ''
        df['category'] = ''
        df['confidence'] = 0.0
        df['reasoning'] = ''
        
        # Process in batches
        
        async def process_all_batches():
            # Process inflow transactions
            if len(partitioned_data['inflow']) > 0:
                print(f"\nProcessing {len(partitioned_data['inflow'])} inflow transactions...")
                await process_partition(partitioned_data['inflow'], 'inflow', df)
            
            # Process outflow transactions
            if len(partitioned_data['outflow']) > 0:
                print(f"\nProcessing {len(partitioned_data['outflow'])} outflow transactions...")
                await process_partition(partitioned_data['outflow'], 'outflow', df)
        
        async def process_partition(partition_df: pd.DataFrame, transaction_type: str, main_df: pd.DataFrame):
            """Process a partition of transactions (inflow or outflow) using parallel batches."""
            # Create semaphore for controlling parallel processing
            semaphore = asyncio.Semaphore(self.max_parallel)
            
            # Create all batch tasks
            batch_tasks = []
            
            for i in range(0, len(partition_df), self.batch_size):
                batch_end = min(i + self.batch_size, len(partition_df))
                batch = partition_df.iloc[i:batch_end]
                batch_num = i//self.batch_size + 1
                
                # Create a task for this batch
                task = self._process_single_batch(batch, batch_num, transaction_type, main_df, semaphore)
                batch_tasks.append(task)
            
            # Process all batches in parallel
            print(f"Starting parallel processing of {len(batch_tasks)} {transaction_type} batches (max {self.max_parallel} concurrent)")
            results = await asyncio.gather(*batch_tasks, return_exceptions=True)
            
            # Report results
            successful_batches = sum(1 for result in results if result is True)
            failed_batches = len(results) - successful_batches
            
            print(f"{transaction_type.capitalize()} partition complete: {successful_batches} successful, {failed_batches} failed batches")
        
        # Run the async processing
        asyncio.run(process_all_batches())
        
        # Generate and print summary
        self._print_processing_summary(df)
        
        # Save results
        df.to_csv(output_file, index=False)

    def _print_processing_summary(self, df: pd.DataFrame) -> None:
        """Print a summary of the processing results."""
        total_rows = len(df)
        
        # Count successful rows (have a category and no error in reasoning)
        successful_rows = len(df[(df['category'] != '') & (~df['reasoning'].str.startswith('ERROR:', na=False))])
        
        # Count rows without a category (but not errors)
        no_category_rows = len(df[(df['category'] == '') & (~df['reasoning'].str.startswith('ERROR:', na=False))])
        
        # Count error rows
        error_rows = len(df[df['reasoning'].str.startswith('ERROR:', na=False)])
        
        print(f"\n{'='*50}")
        print(f"PROCESSING SUMMARY")
        print(f"{'='*50}")
        print(f"Total transactions processed: {total_rows}")
        print(f"✅ Successful classifications: {successful_rows}")
        print(f"⚠️  No category assigned: {no_category_rows}")
        print(f"❌ Errors encountered: {error_rows}")
        print(f"{'='*50}")
        
        # Additional breakdown by transaction type
        if 'inflow/outflow' in df.columns:
            inflow_successful = len(df[(df['inflow/outflow'] == 'Inflow') & (df['category'] != '') & (~df['reasoning'].str.startswith('ERROR:', na=False))])
            outflow_successful = len(df[(df['inflow/outflow'] == 'Outflow') & (df['category'] != '') & (~df['reasoning'].str.startswith('ERROR:', na=False))])
            inflow_errors = len(df[(df['inflow/outflow'] == 'Inflow') & (df['reasoning'].str.startswith('ERROR:', na=False))])
            outflow_errors = len(df[(df['inflow/outflow'] == 'Outflow') & (df['reasoning'].str.startswith('ERROR:', na=False))])
            
            print(f"\nBreakdown by transaction type:")
            print(f"Inflow - Successful: {inflow_successful}, Errors: {inflow_errors}")
            print(f"Outflow - Successful: {outflow_successful}, Errors: {outflow_errors}")

        print("\n")

    def _get_inflow_system_prompt(self) -> str:
        """Get the system prompt for inflow transaction classification."""
        return """You are a financial classification assistant for Audit Sight.

You are given a JSON array of **INFLOW** bank transactions (positive amounts). For each transaction, assign the most accurate **category** from the predefined INFLOW categories below and provide a reason for the category you selected. This classification will be used by Audit Sight to build a "Cash Proof" workpaper for auditors.

🎯 PURPOSE:
The Cash Proof workpaper helps auditors understand:
- How much banking activity relates to **Revenue** and **Expenses**
- How much relates to **other cash flows** such as transfers, loans, equity movements, and fees

---

🔒 IMPORTANT RULES:
- **Important**: Only use the predefined INFLOW categories below. Do not make up your own categories. If you are not sure, leave the category blank, confidence 0.0.
- These are INFLOW transactions only (positive amounts) - only classify into INFLOW categories.
- If the `ocrolus_tag` is provided, use it as supplemental input—but never override the description or amount logic.

---

📥 SAMPLE INPUT:
```json
[
  {
    "transaction_id": 1,
    "account": "Wells Fargo Checking",
    "date": "2024-07-15",
    "description": "Stripe payout 071524",
    "amount": 1324.00,
    "ocrolus_tag": "paypal"
  }
]
```

✅ SAMPLE OUTPUT:
```json
{
  "classifications": [
    {
      "category": "Revenue",
      "confidence": 0.95,
      "reasoning": "Stripe payout indicates revenue from payment processing"
    }
  ]
}
```

---

📚 AVAILABLE INFLOW CATEGORIES:
**Revenue**
- Payments from customers or clients.
- Example keywords: adyen, adyen n.v., aloha, amazon, ar payment, authorize.net, auto deposit, bank deposit, bigcommerce, braintree, braintree gateway, cash app, cash deposit, cash.app, cashapp, cashapp received, cashapp transfer, chargebee, checkout.com, client payment, clover, customer payment, deposit, deposits, direct deposit, ebay, etsy, lightspeed, lockbox, micros, mobile deposit, payment, paypal, paypal inc, paypal invoice, paypal transfer, paypal.com, pp*, ppd paypal, recurly, recurly inc, remote deposit, revel, shopify, square, square deposit, square inc, square payment, squarespace, squareup, stripe, stripe deposit, stripe inc, stripe payout, stripe transfer, stripe.com, toasttab, touchbistro, venmo, venmo business, wix, woocommerce, worldpay, zelle, zelle from, zelle payment, zelle transfer, zellepay

**Interest Income**
- Interest earned from bank accounts or investments.
- Example keywords: apy, apy interest, apye, boa interest, chase savings interest, checking interest, earned, earned interest, int earned, int inc, interest, interest earned, interest income, or, savings interest, wells fargo interest

**Investment Proceeds**
- Liquidation of investments.
- Example keywords: brokerage transfer, fund redemption, investment, investment liquidation, investment sale, investment withdrawal, liquidated, liquidation proceeds, portfolio withdrawal, redemption, sale, sale of investment, security sold, sell order, sold shares

**Intercompany / Related Party**
- Transfers between related parties or affiliated entities.
- Example keywords: affiliate transfer, and, cash, cash funding, client, due, due from, due to, entities, fees, funding, interco, intercompany, management fee, mgmt, mgmt fees, name, of, owner transfer, parent co, related party, to, variations

**Loan Proceeds**
- Business loans, credit lines.
- Example keywords: bluevine, business loan, capital, capital infusion, capital repayment, credit, disbursement, fundbox, kabbage, line, line of credit, loan, loan advance, loan disbursement, loan draw, loan funding, loan payment, loan repay, loc, of, sba, term loan

**Equity Contributions**
- Capital injections from owners.
- Example keywords: capital contrib, capital injection, contrib, draw, equity deposit, member, member contrib, owner contribution, owner deposit, owner funding, partner contribution

**Bank Sweeps**
- Automatic transfers between accounts for liquidity or management.
- Example keywords: cash mgmt, dba, sweep, sweep acct, sweep transfer, sweeps, treasury sweep

**Bank Transfers**
- Internal bank transfers.
- Example keywords: account, account xfer, bank to bank, cross-account, entity transfer, from, internal, internal transfer, linked, linked account, to checking, to savings, transfer, transfer from, transfer to, trn, trns, tsf, xfer, xfer from, xfer to

**Rebates**
- Cash-back or rewards credits.
- Example keywords: cash back, cashback, cc rewards, merchant rebate, points, promo credit, rebate, rebate credit, reward, statement credit

**Returns / Reversals / Refunds**
- Refunds or returned debits.
- Example keywords: cancel, chargeback, dispute resolved, fraud, merchant refund, rebated, refund, refund issued, refunded, reimbursement, return, returned, reversal, reversed, transaction canceled, void

**Tax Refunds**
- Refunds or returned debits.
- Example keywords: eftps, franchise tax refund, gov refund, income tax refund, internal revenue service, irs, irs payment, irs refund, irs tax, name, or, state, state refund, tax return, treasury refund"""

    def _get_outflow_system_prompt(self) -> str:
        """Get the system prompt for outflow transaction classification."""
        return """You are a financial classification assistant for Audit Sight.

You are given a JSON array of **OUTFLOW** bank transactions (negative amounts). For each transaction, assign the most accurate **category** from the predefined OUTFLOW categories below and provide a reason for the category you selected. This classification will be used by Audit Sight to build a "Cash Proof" workpaper for auditors.

🎯 PURPOSE:
The Cash Proof workpaper helps auditors understand:
- How much banking activity relates to **Revenue** and **Expenses**
- How much relates to **other cash flows** such as transfers, loans, equity movements, and fees

---

🔒 IMPORTANT RULES:
- **Important**: Only use the predefined OUTFLOW categories below. Do not make up your own categories. If you are not sure, leave the category blank, confidence 0.0.
- These are OUTFLOW transactions only (negative amounts) - only classify into OUTFLOW categories.
- If the `ocrolus_tag` is provided, use it as supplemental input—but never override the description or amount logic.
- If the transaction is an outflow and only has numeric values and no characters in the description it is always placed in the Checks category.

---

📥 SAMPLE INPUT:
```json
[
  {
    "transaction_id": 1,
    "account": "Wells Fargo Checking",
    "date": "2024-07-15",
    "description": "ADP Payroll",
    "amount": -2500.00,
    "ocrolus_tag": "payroll"
  }
]
```

✅ SAMPLE OUTPUT:
```json
{
  "classifications": [
    {
      "category": "Payroll and Taxes",
      "confidence": 0.95,
      "reasoning": "ADP Payroll indicates employee payroll processing"
    }
  ]
}
```

---

🔎 Additional Classification Guidance

🧾 Taxes vs. Payroll
Taxes should include state and income taxes (e.g., IRS payments, state franchise taxes).


Do NOT include payroll-related tax payments here.


Payroll-related tax transactions should be categorized as Payroll and Taxes.


✅ Example:
 "Preauthorized Wd ADP TAXADP TAX250220 KBDYF 022104A01" → This is Payroll and Taxes, not general Taxes, because it includes ADP, a known payroll provider.

🧾 Bills via Expense Management Platforms
When the transaction includes references to expense platforms like:


bill.com, Ramp, Tipalti, Stampli, Airbase, Sage Intacct, or MineralTree


These transactions should always be categorized as Bills, even if the actual vendor name is not a utility or traditional biller.


✅ Example:
 "Preauthorized Wd BILL.comPAYABLESBUSINESS SOLUTION PARTNERS LLC BILL.com 0150DUOPCKD3C71 MULTIPLE INVOICE" → This is Bills, due to the presence of bill.com.

🔁 Bank Transfers vs. Wires
Only classify as Bank Transfers if the money is moving between accounts owned by the same company (e.g., checking to savings).


Just seeing terms like "WT", "Wire", or "Transfer" is not enough—these might indicate third-party wire payments and should not default to Bank Transfers.


❌ Example:
 "Term - out Wt Intl Usd 2320869322FUNDACJA ROZWOJU P BREXPLPWMBKTMG PAYMENT MAKSYMKLOCHKO" → This is not a Bank Transfer. It's a wire payment to an external party, despite containing "WT".

👥 Related Parties
Related Party transactions should involve known affiliate entities, owners, or parent companies.


Do not categorize independent contractors or vendors as Related Parties, even if the description seems familiar.


When uncertain, err on the side of caution and do not use Related Party instead use Bills.

---

📚 AVAILABLE OUTFLOW CATEGORIES:
**Payroll and Taxes**
- Employee wages.
- Example keywords: 941, adp, adp payroll, adp run, automatic data processing, benefits, eftps, employee pay, gsto, gusto, gusto payroll, hr block, intuit payroll, paychex, payroll, payroll run, payroll tax, quickbooks payroll, tax, wage

**Bills**
- Water, electric, gas, phone.
- Example keywords: at&t, cellco, comcast, coned, dominion energy, duke energy, electric, gas, georgia power, pg&e, power, spectrum, utilities, utility, verizon, water, xfinity

**Taxes**
- Federal, state, or local.
- Example keywords: city tax, eftps, estimated tax, federal tax, franchise, franchise tax, gov tax, income tax, internal revenue service, irs, irs payment, irs tax, local tax, revenue dept, sales tax, state, tax, tax payment

**Credit Card and Digital Payments**
- Business debit card usage.
- Example keywords: american express, amex, apple card, apple pay, bank card, cap one, capital one, card, cash app, cash.app, cashapp, cashapp transfer, cc payment, chase ink, credit card, debit, debit card, discover, google pay, mastercard, samsung pay, venmo, visa, zelle, zelle payment, zelle transfer, zellepay

**Checks**
- Manual payments issued by check.
- Example keywords: check, check #, check no, check number, chk, issued check, manual check, number, only

**Processing Fees**
- Peer-to-peer or mobile apps.
- Example keywords: auto deposit, bank deposit, braintree, cash app, cash.app, cashapp, cashapp transfer, deposit, deposits, direct deposit, merchant fee, payment, payment processor, payout fee, paypal, paypal inc, paypal transfer, paypal.com, platform fee, pp*, ppd paypal, processing fee, shopify, square, square deposit, square inc, square payment, squareup, stripe, stripe deposit, stripe inc, stripe payout, stripe transfer, stripe.com, transaction fee, zelle, zelle payment, zelle transfer, zellepay

**NSF / Overdraft / Bank Fees**
- Overdraft, non-sufficient funds.
- Example keywords: atm fee, bank fee, fee, insufficient funds, maintenance fee, monthly fee, nsf, nsf fee, od fee, overdraft, returned item, service charge

**Intercompany / Related Party**
- Transfers between related parties or affiliated entities.
- Example keywords: affiliate transfer, cash funding, client, due, due from, due to, entities, fees, funding, interco, intercompany, management fee, mgmt, mgmt fees, name, of, owner transfer, parent co, related party, to, variations

**Bank Transfers**
- Transfers to another business or owner.
- Example keywords: account, account xfer, bank to bank, cross-account, entity transfer, from, internal, internal transfer, linked, linked account, to checking, to savings, transfer, transfer from, transfer to, trn, trns, tsf, xfer, xfer from, xfer to

**Bank Sweeps**
- Automatic transfers between accounts for liquidity or management.
- Example keywords: cash mgmt, dba, sweep, sweep acct, sweep transfer, sweeps, treasury sweep

**Loan Payments**
- Any loan or credit repayment.
- Example keywords: bluevine, capital, capital repayment, credit, debt service, disbursement, fundbox, interest payment, kabbage, line, line of credit, loan, loan disbursement, loan payment, loan repay, loc, of, payment, principal payment, repayment, sba, term loan

**Equity Distributions**
- Capital dis to owners.
- Example keywords: capital return, contrib, distribution, draw, equity payout, equity withdrawal, member, member contrib, owner draw, owner withdrawal, partner distribution

**Returns / Reversals / Refunds**
- Refunds or returned debits.
- Example keywords: cancel, chargeback, dispute resolved, fraud, merchant refund, rebated, refund, refund issued, refunded, reimbursement, return, returned, reversal, reversed, transaction canceled, void"""
