"""Tests for transaction classifier."""

import pytest
import pandas as pd
import tempfile
import os
from pathlib import Path
from unittest.mock import Mock, AsyncMock, patch
from transaction_classifier.classifier import TransactionClassifier, TransactionCategory


@pytest.fixture
def sample_transactions():
    """Sample transaction data for testing using real banking data."""
    return pd.DataFrame([
        {
            'postedDate': '2025-03-27',
            'description': 'Preauthorized Wd BILL.comPAYABLESBUSINESS SOLUTION PARTNERS LLC BILL.com 015TIXPUGIE1051 INV INV32658',
            'amount': -518.00
        },
        {
            'postedDate': '2025-01-09', 
            'description': 'Preauthorized Wd STANDARD of OREGXB05DDTRANS : 4147699',
            'amount': -159.00
        },
        {
            'postedDate': '2025-03-21',
            'description': 'Transfer Debit Dp - Ls REF 0801321LFUNDS TRANSFER TOLOAN *********** NOTE 5001FROM',
            'amount': -115000.00
        },
        {
            'postedDate': '2025-01-22',
            'description': 'Preauthorized Wd ADP PAY - BY - PAYPAY - BY - PAY250122 773075321398DYF',
            'amount': -172.40
        },
        {
            'postedDate': '2025-03-21',
            'description': 'Preauthorized Wd CONT ROCKY MOUNTHSACONTRBT250320 599720',
            'amount': -1937.49
        }
    ])


@pytest.fixture
def expected_categories():
    """Expected categories for sample transactions based on real banking data."""
    return [
        TransactionCategory(
            category='Bills',
            confidence=0.95,
            reasoning='Bill.com payment platform indicates bill payment'
        ),
        TransactionCategory(
            category='Bills',
            confidence=0.90,
            reasoning='Standard insurance payment'
        ),
        TransactionCategory(
            category='Loan Payments',
            confidence=0.95,
            reasoning='Transfer to loan account indicates loan payment'
        ),
        TransactionCategory(
            category='Payroll and Taxes',
            confidence=0.95,
            reasoning='ADP payroll processing fee'
        ),
        TransactionCategory(
            category='Bills',
            confidence=0.85,
            reasoning='Healthcare contribution payment'
        )
    ]


class TestTransactionClassifier:
    """Test cases for TransactionClassifier."""
    
    @patch.dict(os.environ, {'OPENAI_API_KEY': 'test-key'})
    def test_init(self):
        """Test classifier initialization."""
        classifier = TransactionClassifier(model="gpt-4o-mini")
        assert classifier.model_name == "gpt-4o-mini"
        assert classifier.batch_size == 50
    
    @patch.dict(os.environ, {'OPENAI_API_KEY': 'test-key'})
    def test_format_transactions_for_llm(self, sample_transactions):
        """Test transaction formatting for LLM."""
        classifier = TransactionClassifier(model="gpt-4o-mini")
        formatted = classifier._format_transactions_for_llm(sample_transactions)
        
        assert "BILL.com" in formatted
        assert "STANDARD of OREG" in formatted
        assert "ADP PAY" in formatted
        assert "-518.0" in formatted or "-518.00" in formatted
    
    @patch.dict(os.environ, {'OPENAI_API_KEY': 'test-key'})
    def test_load_csv_data(self, sample_transactions):
        """Test loading CSV data."""
        classifier = TransactionClassifier(model="gpt-4o-mini")
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False) as f:
            sample_transactions.to_csv(f.name, index=False)
            csv_path = Path(f.name)
        
        try:
            df = classifier._load_data(csv_path)
            assert len(df) == 5
            assert 'description' in df.columns
            assert 'amount' in df.columns
        finally:
            csv_path.unlink()
    
    @patch.dict(os.environ, {'OPENAI_API_KEY': 'test-key'})
    def test_load_parquet_data(self, sample_transactions):
        """Test loading Parquet data."""
        classifier = TransactionClassifier(model="gpt-4o-mini")
        
        with tempfile.NamedTemporaryFile(suffix='.parquet', delete=False) as f:
            sample_transactions.to_parquet(f.name, index=False)
            parquet_path = Path(f.name)
        
        try:
            df = classifier._load_data(parquet_path)
            assert len(df) == 5
            assert 'description' in df.columns
            assert 'amount' in df.columns
        finally:
            parquet_path.unlink()
    
    @patch.dict(os.environ, {'OPENAI_API_KEY': 'test-key'})
    def test_unsupported_file_format(self):
        """Test error handling for unsupported file formats."""
        classifier = TransactionClassifier(model="gpt-4o-mini")
        
        with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as f:
            xlsx_path = Path(f.name)
        
        try:
            with pytest.raises(ValueError, match="Unsupported file format"):
                classifier._load_data(xlsx_path)
        finally:
            xlsx_path.unlink()


@pytest.mark.anyio(backend="asyncio")
@patch.dict(os.environ, {'OPENAI_API_KEY': 'test-key'})
async def test_classify_batch(sample_transactions, expected_categories):
    """Test batch classification (mocked)."""
    classifier = TransactionClassifier(model="gpt-4o-mini")
    
    # Mock the outflow agent (since sample transactions have negative amounts)
    mock_result = Mock()
    mock_result.output.classifications = expected_categories
    classifier.outflow_agent.run = AsyncMock(return_value=mock_result)
    
    result = await classifier._classify_batch(sample_transactions, 'outflow')
    
    assert len(result) == 5
    assert result[0].category == 'Bills'
    assert result[1].category == 'Bills'
    assert result[2].category == 'Loan Payments'
    assert result[3].category == 'Payroll and Taxes'
    assert result[4].category == 'Bills'


@patch.dict(os.environ, {'OPENAI_API_KEY': 'test-key'})
def test_system_prompt():
    """Test that system prompt contains expected categories."""
    classifier = TransactionClassifier(model="gpt-4o-mini")
    prompt = classifier._get_outflow_system_prompt()
    
    expected_categories = [
        'Payroll and Taxes',
        'Bills', 
        'Taxes',
        'Credit Card and Digital Payments',
        'Checks'
    ]
    
    for category in expected_categories:
        assert category in prompt
    
    assert 'confidence' in prompt
    assert 'reasoning' in prompt