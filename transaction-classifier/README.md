# Transaction Classifier

A command-line application that uses Large Language Models to classify banking transactions into categories. Built with Python, Pydantic AI, DuckDB, and OpenAI's GPT models.

## Features

- **Flexible Input**: Supports both CSV and Parquet file formats
- **Batch Processing**: Processes transactions in configurable batches (default: 50)
- **LLM Classification**: Uses GPT models with structured output via Pydantic AI
- **Docker Support**: Containerized deployment option
- **Comprehensive Testing**: Unit tests and integration tests for accuracy validation

## Installation

### Using uv (Recommended)

```bash
# Clone or create the project
cd transaction-classifier

# Install dependencies
uv sync

# Run the application
cp .env.example .env # set the OPENAI_API_KEY
uv run transaction-classifier input.csv output.csv
```

### Using Docker

```bash
# Build the Docker image
docker build -t transaction-classifier .

# Run with docker-compose
export OPENAI_API_KEY="your-api-key-here"
docker-compose up
```

## Usage

### Command Line

#### Available Options

- `--batch-size`: Number of transactions to process in each batch (default: 50)
- `--model`: Model to use for classification (default: "openai:gpt-4.1")
- `--max-retries`: Maximum number of retry attempts for failed batches (default: 1)
- `--max-parallel`: Maximum number of batches to process in parallel (default: 3)
- `--openai-url`: Custom OpenAI-compatible API base URL (e.g., http://localhost:11434/v1 for Ollama)

#### Usage Examples

```bash
# Basic usage. The default model is openai:gpt-4.1 (OPENAI_API_KEY must be set)
uv run transaction-classifier input.csv output.csv

# With custom batch size and Anthropic Claude 4 Sonnet model (ANTHROPIC_API_KEY must be set)
uv run transaction-classifier --batch-size 50 --model anthropic:claude-sonnet-4-******** input.csv output.csv 

# Example using Bedrock with Claude 4 Sonnet (AWS_ACCESS_KEY_ID, AWS_SECRET_ACCESS_KEY and AWS_DEFAULT_REGION must be set)
uv run transaction-classifier --model bedrock:us.anthropic.claude-sonnet-4-********-v1:0 data/banking_transactions_Superstep-Zaelab-TMG\ -\ NS-.csv data/output-claude-4-sonnet.csv 

# Example with custom retry and parallel settings
uv run transaction-classifier --batch-size 25 --max-retries 3 --max-parallel 5 input.csv output.csv

# Example using Ollama (local server)
uv run transaction-classifier --model "llama3.2" --openai-url "http://localhost:11434/v1" input.csv output.csv

# Example using Ollama (remote server)
uv run transaction-classifier --model "qwen2.5-coder:7b" --openai-url "http://************:11434/v1" input.csv output.csv
```


### OpenAI-Compatible APIs

The `--openai-url` flag allows you to use OpenAI-compatible APIs such as Ollama, which can run local models or connect to remote servers. This is useful for:

- **Local Models**: Run models locally using Ollama without sending data to external APIs
- **Custom Endpoints**: Connect to private or self-hosted OpenAI-compatible services
- **Cost Optimization**: Use cheaper alternatives or free local models

#### Using with Ollama

1. Install and start Ollama:
```bash
# Install Ollama (see https://ollama.ai for installation instructions)
ollama pull llama3.2  # Download the model you want to use
ollama serve           # Start the Ollama server
```

2. Use with the transaction classifier:
```bash
uv run transaction-classifier --model "llama3.2" --openai-url "http://localhost:11434/v1" input.csv output.csv
```

### Environment Variables
Depending on the provider, environment variables need to be set for the API calls.

The environment variable can be set via a `.env` file (see `.env.example`) or at the system level.

Example OpenAI API key:
```bash
export OPENAI_API_KEY="your-api-key-here"
```

**Note**: When using `--openai-url` with local models like Ollama, you typically don't need to set API keys.

### Input Data Format

Your CSV or Parquet file should contain transaction data with these common columns:
- `date` : Transaction date
- `description`: Transaction description
- `amount`: Transaction amount
- `ocrolus_tag`: (Optional) tags from ocrolus
- `account`: (Optional) Account name if available
- Additional columns will be preserved in the output

Example CSV:
```csv
date,description,amount,account
2024-01-15,STARBUCKS COFFEE,-5.75,Checking
2024-01-15,SHELL GAS STATION,-45.30,Checking
2024-01-16,AMAZON.COM,-23.99,Credit Card
```

### Output

The output CSV will contain all original columns plus:
- `category`: Assigned category (e.g., "Revenue", "Payroll and Taxes")
- `confidence`: Confidence score (0.0 to 1.0)
- `reasoning`: Brief explanation of the classification

## Categories

The classifier uses these predefined categories:

## Inflow Categories (Positive Amounts)

- **Revenue**: Payments from customers or clients (e.g., Stripe, PayPal, Square, Shopify)
- **Interest Income**: Interest earned from bank accounts or investments
- **Investment Proceeds**: Liquidation of investments
- **Intercompany / Related Party**: Transfers between related parties or affiliated entities
- **Loan Proceeds**: Business loans, credit lines
- **Equity Contributions**: Capital injections from owners
- **Bank Sweeps**: Automatic transfers between accounts for liquidity or management
- **Bank Transfers**: Internal bank transfers
- **Rebates**: Cash-back or rewards credits
- **Returns / Reversals / Refunds**: Refunds or returned debits
- **Tax Refunds**: Government tax refunds

## Outflow Categories (Negative Amounts)

- **Payroll and Taxes**: Employee wages and payroll taxes
- **Bills**: Water, electric, gas, phone, utilities
- **Taxes**: Federal, state, or local taxes
- **Credit Card and Digital Payments**: Business debit card usage
- **Checks**: Manual payments issued by check
- **Processing Fees**: Payment processing fees (Stripe, PayPal, etc.)
- **NSF / Overdraft / Bank Fees**: Overdraft, non-sufficient funds, bank fees
- **Intercompany / Related Party**: Transfers between related parties or affiliated entities
- **Bank Transfers**: Transfers to another business or owner
- **Bank Sweeps**: Automatic transfers between accounts for liquidity or management
- **Loan Payments**: Any loan or credit repayment
- **Equity Distributions**: Capital distributions to owners
- **Returns / Reversals / Refunds**: Refunds or returned debits

## Development

### Running Tests

```bash
# Run all tests
uv run pytest

# Run with coverage
uv run pytest --cov=transaction_classifier

# Run specific test file
uv run pytest tests/test_classifier.py
```

### Code Formatting

```bash
# Format code
uv run black src/ tests/

# Lint code
uv run ruff check src/ tests/
```

## Configuration

### Models

Supported OpenAI models:
- See Pydantic AI docs https://ai.pydantic.dev/models/. Essentially you make sure the respective key(s) is set for the provider and specify a of the form `openai:gpt-4.1` or `anthropic:claude-sonnet-4-********`. If the required environment variables haven't been set the program will error out with a message indicating the required variable.
  
### Batch Size

Adjust batch size based on your needs:
- Smaller batches: More API calls, better error handling
- Larger batches: Fewer API calls, more cost-effective


