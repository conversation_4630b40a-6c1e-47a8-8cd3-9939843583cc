[project]
name = "transaction-classifier"
version = "0.1.0"
description = "A command line application for classifying banking transactions using LLM"
readme = "README.md"
requires-python = ">=3.13"
dependencies = [
    "pydantic-ai>=0.4.0",
    "duckdb>=1.1.0",
    "openai>=1.54.0",
    "click>=8.1.0",
    "pandas>=2.2.0",
    "pyarrow>=17.0.0",
    "pydantic>=2.10.0",
    "python-dotenv>=1.0.0",
    "pydantic-ai-slim[retries]>=0.4.11",
]

[project.scripts]
transaction-classifier = "transaction_classifier.cli:main"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["src/transaction_classifier"]

[tool.uv]
dev-dependencies = [
    "pytest>=8.0.0",
    "pytest-cov>=5.0.0",
    "pytest-asyncio>=1.1.0",
    "black>=24.0.0",
    "ruff>=0.7.0",
]

[tool.pytest.ini_options]
markers = [
    "anyio: marks tests as requiring anyio",
]
filterwarnings = [
    "ignore::pytest.PytestUnknownMarkWarning",
]
addopts = "-k 'not trio'"
